import {
  powerUnitSchema,
  PowerUnitSchema,
  pumpTopSchema,
  PumpTopSchema,
  siteVoltageSchema,
  SiteVoltageSchema,
} from "@routes/build-and-price/-schemas/build";
import Decimal from "decimal.js";
import { assign, setup } from "xstate";

// Define context type for guard functions
type GuardContext = {
  step: number;
  build: {
    productName: string;
    pumpTop: PumpTopSchema;
    siteVoltage: SiteVoltageSchema;
    powerUnit: PowerUnitSchema;
  };
  pricing: {
    pumpTop: PumpTopSchema;
    siteVoltage: SiteVoltageSchema;
    powerUnit: PowerUnitSchema;
    listPrice: string;
  };
};

// Shared guard logic functions to avoid duplication
const guardLogic = {
  pumpTop: (context: GuardContext) => {
    console.log({ context });
    return true;
  },
  build: (context: GuardContext) => {
    return (
      context.build.pumpTop.pump_top_id !== 0 &&
      context.build.productName !== ""
    );
  },
  options: (context: GuardContext) =>
    context.build.powerUnit.power_unit_id !== 0 &&
    context.build.siteVoltage.site_voltage_id !== 0 &&
    context.build.pumpTop.pump_top_id !== 0 &&
    context.build.productName !== "",
};

export const machine = setup({
  types: {
    context: {} as {
      step: number;
      build: {
        productName: string;
        pumpTop: PumpTopSchema;
        siteVoltage: SiteVoltageSchema;
        powerUnit: PowerUnitSchema;
      };
      pricing: {
        pumpTop: PumpTopSchema;
        siteVoltage: SiteVoltageSchema;
        powerUnit: PowerUnitSchema;
        listPrice: string;
      };
    },
    events: {} as
      | { type: "setProductName"; productName: string }
      | { type: "setPumpTop"; pumpTop: PumpTopSchema }
      | { type: "setSiteVoltage"; siteVoltage: SiteVoltageSchema }
      | { type: "setPowerUnit"; powerUnit: PowerUnitSchema }
      | { type: "jumpToStep"; step: number },
    input: {} as {
      step?: number;
      build?: {
        pumpTop: PumpTopSchema | undefined;
        siteVoltage: SiteVoltageSchema | undefined;
        powerUnit: PowerUnitSchema | undefined;
      };
    },
  },
  actions: {
    setPumpTop: assign(({ context, event }) => {
      if (event.type === "setPumpTop") {
        return {
          build: {
            ...context.build,
            pumpTop: event.pumpTop,
          },
          pricing: {
            ...context.pricing,
            pumpTop: event.pumpTop,
            listPrice: Decimal(event.pumpTop.msrp_cad)
              .add(context.pricing.siteVoltage.msrp_cad)
              .add(context.pricing.powerUnit.msrp_cad)
              .toFixed(2),
          },
        };
      }
      return {};
    }),
    setSiteVoltage: assign(({ context, event }) => {
      if (event.type === "setSiteVoltage") {
        return {
          build: {
            ...context.build,
            siteVoltage: event.siteVoltage,
          },
          pricing: {
            ...context.pricing,
            siteVoltage: event.siteVoltage,
            listPrice: Decimal(context.pricing.pumpTop.msrp_cad)
              .add(event.siteVoltage.msrp_cad)
              .add(context.pricing.powerUnit.msrp_cad)
              .toFixed(2),
          },
        };
      }
      return {};
    }),
    setPowerUnit: assign(({ context, event }) => {
      if (event.type === "setPowerUnit") {
        return {
          build: {
            ...context.build,
            powerUnit: event.powerUnit,
          },
          pricing: {
            ...context.pricing,
            powerUnit: event.powerUnit,
            listPrice: Decimal(context.pricing.pumpTop.msrp_cad)
              .add(context.pricing.siteVoltage.msrp_cad)
              .add(event.powerUnit.msrp_cad)
              .toFixed(2),
          },
        };
      }
      return {};
    }),
    jumpToStep: assign(({ context, event }) => {
      if (event.type === "jumpToStep") {
        const targetStep = event.step;
        console.log("jumpToStep action triggered", {
          targetStep,
          currentStep: context.step,
        });

        // Use the shared guard logic functions
        const stepGuards = {
          1: guardLogic.pumpTop(context), // PumpTop guard
          2: guardLogic.build(context), // Build guard
          3: guardLogic.options(context), // Options guard
        };

        // Check if the target step is valid
        const isValidStep = stepGuards[targetStep as keyof typeof stepGuards];

        if (isValidStep) {
          console.log(
            `Jumping to step ${targetStep} - guard validation passed`,
          );
          return { step: targetStep };
        } else {
          console.log(
            `Cannot jump to step ${targetStep} - guard validation failed`,
          );
          return {}; // Don't update step if guard fails
        }
      }
      return {};
    }),
  },
  guards: {
    options: ({ context }) => guardLogic.options(context),
    build: ({ context }) => guardLogic.build(context),
    pumpTop: ({ context }) => guardLogic.pumpTop(context),
  },
}).createMachine({
  id: "buildMachine",
  initial: "Loading",
  context: ({ input }) => ({
    step: input.step ?? 1,
    build: {
      productName: input.build?.pumpTop?.unit_type ?? "",
      pumpTop: input.build?.pumpTop ?? pumpTopSchema.parse({}),
      siteVoltage: input.build?.siteVoltage ?? siteVoltageSchema.parse({}),
      powerUnit: input.build?.powerUnit ?? powerUnitSchema.parse({}),
    },
    pricing: {
      pumpTop: input.build?.pumpTop ?? pumpTopSchema.parse({}),
      siteVoltage: input.build?.siteVoltage ?? siteVoltageSchema.parse({}),
      powerUnit: input.build?.powerUnit ?? powerUnitSchema.parse({}),
      listPrice: Decimal(input.build?.powerUnit?.msrp_cad ?? 0)
        .add(Decimal(input.build?.siteVoltage?.msrp_cad ?? 0))
        .add(Decimal(input.build?.pumpTop?.msrp_cad ?? 0))
        .toFixed(2),
    },
  }),
  on: {},
  states: {
    Loading: {
      always: [
        { target: "Options", guard: "options" },
        { target: "Build", guard: "build" },
        { target: "PumpTop", guard: "pumpTop" },
      ],
    },
    PumpTop: {
      on: {
        setPumpTop: { actions: "setPumpTop" },
        setSiteVoltage: { actions: "setSiteVoltage" },
        setPowerUnit: { actions: "setPowerUnit" },
        jumpToStep: { actions: "jumpToStep" },
      },
      meta: {
        step: 1,
      },
    },
    Build: {
      on: {
        setPumpTop: { actions: "setPumpTop" },
        setSiteVoltage: { actions: "setSiteVoltage" },
        setPowerUnit: { actions: "setPowerUnit" },
        jumpToStep: { actions: "jumpToStep" },
      },
      meta: {
        step: 2,
      },
    },
    Options: {
      on: {
        setPumpTop: { actions: "setPumpTop" },
        setSiteVoltage: { actions: "setSiteVoltage" },
        setPowerUnit: { actions: "setPowerUnit" },
        jumpToStep: { actions: "jumpToStep" },
      },
      meta: {
        step: 3,
      },
    },
  },
});
