import {
  powerUnitSchema,
  PowerUnitSchema,
  pumpTopSchema,
  PumpTopSchema,
  siteVoltageSchema,
  SiteVoltageSchema,
} from "@routes/build-and-price/-schemas/build";
import Decimal from "decimal.js";
import { assign, setup } from "xstate";

export const machine = setup({
  types: {
    context: {} as {
      step: number;
      build: {
        productName: string;
        pumpTop: PumpTopSchema;
        siteVoltage: SiteVoltageSchema;
        powerUnit: PowerUnitSchema;
      };
      pricing: {
        pumpTop: PumpTopSchema;
        siteVoltage: SiteVoltageSchema;
        powerUnit: PowerUnitSchema;
        listPrice: string;
      };
    },
    events: {} as
      | { type: "setProductName"; productName: string }
      | { type: "setPumpTop"; pumpTop: PumpTopSchema }
      | { type: "setSiteVoltage"; siteVoltage: SiteVoltageSchema }
      | { type: "setPowerUnit"; powerUnit: PowerUnitSchema }
      | { type: "jumpToStep"; step: number },
    input: {} as {
      step?: number;
      build?: {
        pumpTop: PumpTopSchema | undefined;
        siteVoltage: SiteVoltageSchema | undefined;
        powerUnit: PowerUnitSchema | undefined;
      };
    },
  },
  actions: {
    setProductName: (_, params: { productName: string }) =>
      assign(({ context }) => {
        return {
          pricing: {
            ...context.pricing,
            listPrice: Decimal(context.pricing.pumpTop.msrp_cad)
              .add(context.pricing.siteVoltage.msrp_cad)
              .add(context.pricing.powerUnit.msrp_cad)
              .toFixed(2),
            productName: params.productName,
          },
        };
      }),
    setPumpTop: (_, params: { pumpTop: PumpTopSchema }) =>
      assign(({ context }) => {
        console.log({ params });
        return {
          pricing: {
            ...context.pricing,
            listPrice: Decimal(params.pumpTop.msrp_cad)
              .add(context.pricing.siteVoltage.msrp_cad)
              .add(context.pricing.powerUnit.msrp_cad)
              .toFixed(2),
            pumpTop: params.pumpTop,
          },
        };
      }),
    setSiteVoltage: (_, params: { siteVoltage: SiteVoltageSchema }) =>
      assign(({ context }) => {
        return {
          pricing: {
            ...context.pricing,
            listPrice: Decimal(context.pricing.pumpTop.msrp_cad)
              .add(params.siteVoltage.msrp_cad)
              .add(context.pricing.powerUnit.msrp_cad)
              .toFixed(2),
            siteVoltage: params.siteVoltage,
          },
        };
      }),
    setPowerUnit: (_, params: { powerUnit: PowerUnitSchema }) =>
      assign(({ context }) => {
        return {
          pricing: {
            ...context.pricing,
            listPrice: Decimal(context.pricing.pumpTop.msrp_cad)
              .add(context.pricing.siteVoltage.msrp_cad)
              .add(params.powerUnit.msrp_cad)
              .toFixed(2),
            powerUnit: params.powerUnit,
          },
        };
      }),
    jumpToStep: (_, params: { step: number }) =>
      assign({
        step: params.step,
      }),
  },
  guards: {
    options: ({ context }) =>
      context.build.powerUnit.power_unit_id !== 0 &&
      context.build.siteVoltage.site_voltage_id !== 0 &&
      context.build.pumpTop.pump_top_id !== 0 &&
      context.build.productName !== "",
    build: ({ context }) => {
      return (
        context.build.pumpTop.pump_top_id !== 0 &&
        context.build.productName !== ""
      );
    },
    pumpTop: ({ context }) => {
      console.log({ context });
      return true;
    },
  },
}).createMachine({
  id: "buildMachine",
  initial: "Loading",
  context: ({ input }) => ({
    step: input.step ?? 1,
    build: {
      productName: input.build?.pumpTop?.unit_type ?? "",
      pumpTop: input.build?.pumpTop ?? pumpTopSchema.parse({}),
      siteVoltage: input.build?.siteVoltage ?? siteVoltageSchema.parse({}),
      powerUnit: input.build?.powerUnit ?? powerUnitSchema.parse({}),
    },
    pricing: {
      pumpTop: input.build?.pumpTop ?? pumpTopSchema.parse({}),
      siteVoltage: input.build?.siteVoltage ?? siteVoltageSchema.parse({}),
      powerUnit: input.build?.powerUnit ?? powerUnitSchema.parse({}),
      listPrice: Decimal(input.build?.powerUnit?.msrp_cad ?? 0)
        .add(Decimal(input.build?.siteVoltage?.msrp_cad ?? 0))
        .add(Decimal(input.build?.pumpTop?.msrp_cad ?? 0))
        .toFixed(2),
    },
  }),
  states: {
    Loading: {
      always: [
        { target: "Options", guard: "options" },
        { target: "Build", guard: "build" },
        { target: "PumpTop", guard: "pumpTop" },
      ],
    },
    PumpTop: {
      on: {
        setPumpTop: {
          // @ts-expect-error - XState v5 typing issue with string action references
          actions: "setPumpTop",
        },
        setSiteVoltage: {
          // @ts-expect-error - XState v5 typing issue with string action references
          actions: "setSiteVoltage",
        },
        setPowerUnit: {
          // @ts-expect-error - XState v5 typing issue with string action references
          actions: "setPowerUnit",
        },
        jumpToStep: {
          // @ts-expect-error - XState v5 typing issue with string action references
          actions: "jumpToStep",
        },
      },
      meta: {
        step: 1,
      },
    },
    Build: {
      meta: {
        step: 2,
      },
      on: {
        setPumpTop: {
          // @ts-expect-error - XState v5 typing issue with string action references
          actions: "setPumpTop",
        },
        setSiteVoltage: {
          // @ts-expect-error - XState v5 typing issue with string action references
          actions: "setSiteVoltage",
        },
        setPowerUnit: {
          // @ts-expect-error - XState v5 typing issue with string action references
          actions: "setPowerUnit",
        },
        jumpToStep: {
          // @ts-expect-error - XState v5 typing issue with string action references
          actions: "jumpToStep",
        },
      },
    },
    Options: {
      meta: {
        step: 3,
      },
      on: {
        setPumpTop: {
          actions: "setPumpTop",
        },
        setSiteVoltage: {
          actions: "setSiteVoltage",
        },
        setPowerUnit: {
          actions: "setPowerUnit",
        },
        jumpToStep: {
          actions: "jumpToStep",
        },
      },
    },
  },
});
