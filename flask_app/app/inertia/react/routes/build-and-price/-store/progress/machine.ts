import {
  powerUnitSchema,
  PowerUnitSchema,
  pumpTopSchema,
  PumpTopSchema,
  siteVoltageSchema,
  SiteVoltageSchema,
} from "@routes/build-and-price/-schemas/build";
import Decimal from "decimal.js";
import { assign, setup } from "xstate";

export const machine = setup({
  types: {
    context: {} as {
      step: number;
      build: {
        productName: string;
        pumpTop: PumpTopSchema;
        siteVoltage: SiteVoltageSchema;
        powerUnit: PowerUnitSchema;
      };
      pricing: {
        pumpTop: PumpTopSchema;
        siteVoltage: SiteVoltageSchema;
        powerUnit: PowerUnitSchema;
        listPrice: string;
      };
    },
    events: {} as
      | { type: "setProductName"; productName: string }
      | { type: "setPumpTop"; pumpTop: PumpTopSchema }
      | { type: "setSiteVoltage"; siteVoltage: SiteVoltageSchema }
      | { type: "setPowerUnit"; powerUnit: PowerUnitSchema }
      | { type: "jumpToStep"; step: number },
    input: {} as {
      step?: number;
      build?: {
        pumpTop: PumpTopSchema | undefined;
        siteVoltage: SiteVoltageSchema | undefined;
        powerUnit: PowerUnitSchema | undefined;
      };
    },
  },
  actions: {
    setPumpTop: (_, params: { pumpTop: PumpTopSchema }) =>
      assign(({ context }) => {
        console.log({ params });
        return {
          pricing: {
            ...context.pricing,
            listPrice: Decimal(params.pumpTop.msrp_cad)
              .add(context.pricing.siteVoltage.msrp_cad)
              .add(context.pricing.powerUnit.msrp_cad)
              .toFixed(2),
            pumpTop: params.pumpTop,
          },
        };
      }),
    jumpToStep: (_, params: { step: number }) =>
      assign({
        step: params.step,
      }),
  },
  guards: {
    options: ({ context }) =>
      context.build.powerUnit.power_unit_id !== 0 &&
      context.build.siteVoltage.site_voltage_id !== 0 &&
      context.build.pumpTop.pump_top_id !== 0 &&
      context.build.productName !== "",
    build: ({ context }) => {
      return (
        context.build.pumpTop.pump_top_id !== 0 &&
        context.build.productName !== ""
      );
    },
    pumpTop: ({ context }) => {
      console.log({ context });
      return true;
    },
  },
}).createMachine({
  id: "buildMachine",
  initial: "Loading",
  context: ({ input }) => ({
    step: input.step ?? 1,
    build: {
      productName: input.build?.pumpTop?.unit_type ?? "",
      pumpTop: input.build?.pumpTop ?? pumpTopSchema.parse({}),
      siteVoltage: input.build?.siteVoltage ?? siteVoltageSchema.parse({}),
      powerUnit: input.build?.powerUnit ?? powerUnitSchema.parse({}),
    },
    pricing: {
      pumpTop: input.build?.pumpTop ?? pumpTopSchema.parse({}),
      siteVoltage: input.build?.siteVoltage ?? siteVoltageSchema.parse({}),
      powerUnit: input.build?.powerUnit ?? powerUnitSchema.parse({}),
      listPrice: Decimal(input.build?.powerUnit?.msrp_cad ?? 0)
        .add(Decimal(input.build?.siteVoltage?.msrp_cad ?? 0))
        .add(Decimal(input.build?.pumpTop?.msrp_cad ?? 0))
        .toFixed(2),
    },
  }),
  on: {},
  states: {
    Loading: {
      always: [
        { target: "Options", guard: "options" },
        { target: "Build", guard: "build" },
        { target: "PumpTop", guard: "pumpTop" },
      ],
    },
    PumpTop: {
      on: {
        setPumpTop: {
          actions: assign(({ context, event }) => {
            console.log("setPumpTop action triggered in PumpTop state", {
              event,
            });
            return {
              build: {
                ...context.build,
                pumpTop: event.pumpTop,
              },
              pricing: {
                ...context.pricing,
                pumpTop: event.pumpTop,
                listPrice: Decimal(event.pumpTop.msrp_cad)
                  .add(context.pricing.siteVoltage.msrp_cad)
                  .add(context.pricing.powerUnit.msrp_cad)
                  .toFixed(2),
              },
            };
          }),
        },
      },
      meta: {
        step: 1,
      },
    },
    Build: {
      on: {
        setPumpTop: {
          actions: assign(({ context, event }) => {
            console.log("setPumpTop action triggered in Build state", {
              event,
            });
            return {
              build: {
                ...context.build,
                pumpTop: event.pumpTop,
              },
              pricing: {
                ...context.pricing,
                pumpTop: event.pumpTop,
                listPrice: Decimal(event.pumpTop.msrp_cad)
                  .add(context.pricing.siteVoltage.msrp_cad)
                  .add(context.pricing.powerUnit.msrp_cad)
                  .toFixed(2),
              },
            };
          }),
        },
      },
      meta: {
        step: 2,
      },
    },
    Options: {
      on: {
        setPumpTop: {
          actions: assign(({ context, event }) => {
            console.log("setPumpTop action triggered in Options state", {
              event,
            });
            return {
              build: {
                ...context.build,
                pumpTop: event.pumpTop,
              },
              pricing: {
                ...context.pricing,
                pumpTop: event.pumpTop,
                listPrice: Decimal(event.pumpTop.msrp_cad)
                  .add(context.pricing.siteVoltage.msrp_cad)
                  .add(context.pricing.powerUnit.msrp_cad)
                  .toFixed(2),
              },
            };
          }),
        },
      },
      meta: {
        step: 3,
      },
    },
  },
});
