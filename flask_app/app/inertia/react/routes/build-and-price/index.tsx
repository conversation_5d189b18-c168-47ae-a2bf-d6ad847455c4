import { createFileRoute, useSearch } from "@tanstack/react-router";
import { Header } from "./-components/header";
import Progress from "./-components/progress";
import { BuildAndPrice } from "./-components/build-price";
import { zodValidator } from "@tanstack/zod-adapter";
import { searchParamsSchema } from "./-schemas/search-params";
import { BuildAndPriceWizard } from "./-store/progress/context";
import { useStepTwoData } from "./-hooks/step-data";

export const Route = createFileRoute("/")({
  validateSearch: zodValidator(searchParamsSchema),
  component: RouteComponent,
});

function RouteComponent() {
  const { step, build } = useSearch({ from: "/" });
  const { isSuccess, selectedPumpTop, selectedSiteVoltage, selectedPowerUnit } =
    useStepTwoData();
  console.log({ step, build });
  console.log({ selectedPumpTop, isSuccess });
  return (
    <div className="flex flex-grow flex-col items-center justify-start bg-gray-100 p-2 lg:p-8">
      <div className="flex w-full max-w-7xl flex-col gap-2 lg:gap-8">
        <Header />
        {isSuccess ? (
          <BuildAndPriceWizard.Provider
            key={JSON.stringify({ step, build })}
            options={{
              input: {
                step,
                build: {
                  pumpTop: selectedPumpTop,
                  siteVoltage: selectedSiteVoltage,
                  powerUnit: selectedPowerUnit,
                },
              },
            }}
          >
            <Progress />
            <BuildAndPrice />
          </BuildAndPriceWizard.Provider>
        ) : (
          <div className="flex flex-col items-center justify-center gap-4">
            Loading...
          </div>
        )}
      </div>
    </div>
  );
}
