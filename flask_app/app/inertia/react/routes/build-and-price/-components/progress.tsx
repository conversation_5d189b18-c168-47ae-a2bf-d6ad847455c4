import { CheckIcon } from "lucide-react";
import { Link, useSearch } from "@tanstack/react-router";
import { useBuildPriceWizardRef } from "../-store/progress/hooks";

const steps = [
  { id: "1", name: "Select Pump Top", href: "#", status: "complete" },
  { id: "2", name: "Build", href: "#", status: "current" },
  { id: "3", name: "Options", href: "#", status: "upcoming" },
  { id: "4", name: "Request Quote", href: "#", status: "upcoming" },
];

export default function Progress() {
  const search = useSearch({ from: "/" });
  const stepState = search.step - 1;
  const wizard = useBuildPriceWizardRef();
  return (
    <>
      <nav
        aria-label="Progress"
        className="flex items-center justify-center md:hidden"
      >
        <p className="text-sm font-medium">
          Step {stepState + 1} of {steps.length}
        </p>
        <ol role="list" className="ml-8 flex items-center space-x-5">
          {steps.map((step, idx) => (
            <li key={step.name}>
              {idx < stepState ? (
                <Link
                  to="."
                  search={(prev) => ({ ...prev, step: idx + 1 })}
                  type="button"
                  className="bg-ijack-black block size-2.5 cursor-pointer rounded-full border transition-transform duration-200 ease-in-out hover:scale-125"
                >
                  <span className="sr-only">{step.name}</span>
                </Link>
              ) : idx === stepState ? (
                <button
                  type="button"
                  aria-current="step"
                  className="relative flex cursor-pointer items-center justify-center"
                >
                  <span
                    aria-hidden="true"
                    className="absolute flex size-5 p-px"
                  >
                    <span className="bg-ijack-black size-full rounded-full" />
                  </span>
                  <span
                    aria-hidden="true"
                    className="bg-ijack-green relative block size-2.5 cursor-pointer rounded-full"
                  />
                  <span className="sr-only">{step.name}</span>
                </button>
              ) : (
                <button
                  type="button"
                  className="block size-2.5 rounded-full bg-gray-200 hover:bg-gray-400"
                >
                  <span className="sr-only">{step.name}</span>
                </button>
              )}
            </li>
          ))}
        </ol>
      </nav>
      <nav className="hidden w-full md:block" aria-label="Progress">
        <ol role="list" className="space-y-4 md:flex md:space-y-0 md:space-x-8">
          {steps.map((step, idx) => (
            <li
              key={step.name}
              className="transition-transform duration-200 ease-in-out hover:scale-105 md:flex-1"
            >
              {idx < stepState ? (
                <Link
                  to="."
                  search={(prev) => ({ ...prev, step: idx + 1 })}
                  type="button"
                  className="group border-ijack-green flex w-full flex-col border-l-4 py-2 pl-4 text-start md:border-t-4 md:border-l-0 md:pt-4 md:pb-0 md:pl-0"
                >
                  <span className="text-ijack-black flex justify-between text-sm font-medium">
                    Step {step.id}
                    <CheckIcon />
                  </span>
                  <span className="text-sm font-medium">{step.name}</span>
                </Link>
              ) : idx === stepState ? (
                <button
                  onClick={() => {
                    if (
                      wizard
                        .getSnapshot()
                        .can({ type: "canJumpTo", step: idx + 1 })
                    ) {
                      alert("can jump");
                    } else {
                      alert("cannot jump");
                    }
                  }}
                  type="button"
                  aria-current="step"
                  className="border-ijack-green flex w-full flex-col border-l-4 py-2 pl-4 text-start md:border-t-4 md:border-l-0 md:pt-4 md:pb-0 md:pl-0"
                >
                  <span className="text-ijack-black text-sm font-medium">
                    Step {step.id}
                  </span>
                  <span className="text-sm font-medium">{step.name}</span>
                </button>
              ) : (
                <button
                  onClick={() => {
                    if (
                      wizard
                        .getSnapshot()
                        .can({ type: "canJumpTo", step: idx + 1 })
                    ) {
                      alert("can jump");
                    } else {
                      alert("cannot jump");
                    }
                  }}
                  type="button"
                  className="group flex w-full flex-col border-l-4 border-gray-200 py-2 pl-4 text-start hover:border-gray-300 md:border-t-4 md:border-l-0 md:pt-4 md:pb-0 md:pl-0"
                >
                  <span className="text-ijack-grey group-hover:text-ijack-black text-sm font-medium">
                    Step {step.id}
                  </span>
                  <span className="text-sm font-medium">{step.name}</span>
                </button>
              )}
            </li>
          ))}
        </ol>
      </nav>
    </>
  );
}
