import { <PERSON>rro<PERSON>, H3 } from "@/components/ui/typography";
import { formOpts, useAppForm } from "./form/form";
import { $api } from "@/api/web-api";
import { useNavigate, useSearch } from "@tanstack/react-router";
import { But<PERSON> } from "@/components/ui/button";
import React from "react";
import { DesktopCard, MobileCard } from "./form/card";
import { Total } from "../total";
import {
  useBuildPriceWizardSelector,
  useBuildPriceWizardRef,
} from "@routes/build-and-price/-store/progress/hooks";
import { columns } from "./table/columns";
import { ArrowRight } from "lucide-react";
import { pumpTopFormSchema } from "./form/validation";
import { TableField } from "./table/pricing-table";
import { pumpTopSchema } from "@routes/build-and-price/-schemas/build";
import { toast } from "sonner";
import { defaultMeta } from "@routes/build-and-price/-schemas/form-meta";

const PumpTopForm = () => {
  const search = useSearch({ from: "/" });
  const navigate = useNavigate();
  const productName = useBuildPriceWizardSelector(
    (state) => state.context.build.productName,
  );
  const form = useAppForm({
    ...formOpts,
    defaultValues: {
      product_name: productName ?? "",
      pump_top_id: search?.build?.pump_top_id ?? 0,
    },
    validators: {
      onSubmit: pumpTopFormSchema,
    },
    onSubmitMeta: defaultMeta,
    onSubmit: ({ value, meta }) => {
      if (meta.submitAction === "next") {
        navigate({
          to: ".",
          search: (prev) => ({
            ...prev,
            step: 2,
            build: {
              ...search.build,
              product_name: value.product_name,
              pump_top_id: value.pump_top_id,
            },
          }),
        });
      }
    },
    onSubmitInvalid: (errors) => {
      const messages: string[] = [];
      (errors.formApi.getAllErrors().form.errors ?? []).forEach((error) => {
        console.log({ error });
        Object.values(error ?? {}).forEach((err) => {
          err.forEach((e) => {
            if (e.message !== undefined) {
              messages.push(e.message);
            }
          });
        });
      });
      messages.forEach((error) => {
        toast.error(error);
      });
    },
  });
  const pumpTopRef = React.useRef<HTMLHeadingElement>(null);
  const valueX = useBuildPriceWizardSelector(
    (state) => state.context.pricing.pumpTop.description,
  );
  const wizard = useBuildPriceWizardRef();
  return (
    <div className="flex w-full flex-col items-center justify-start gap-8 lg:gap-12">
      X State Product Name: "{valueX}"
      <DesktopCard nextRef={pumpTopRef} form={form} />
      <MobileCard form={form} nextRef={pumpTopRef} />
      <form.Subscribe
        selector={(state) => state.values.product_name}
        children={(value) => {
          const { isSuccess, isLoading, data } = $api.useQuery(
            "get",
            "/v1/pricing/pump-top/",
            {
              params: { query: { unit_type: value } },
            },
            {
              enabled: value !== "",
            },
          );
          const pumpTopField = "pump_top_id";
          if (value === "") {
            return null;
          }
          return (
            <form.AppField
              name={pumpTopField}
              children={(field) => (
                <div className="flex w-full flex-col items-center justify-start gap-4">
                  <div className="flex w-full flex-row flex-wrap items-center gap-2 text-center">
                    <H3 ref={pumpTopRef} className="col-span-full">
                      Select {form.getFieldValue("product_name")} Pump Top
                    </H3>
                    {field.state.meta.errors.length > 0 && (
                      <>
                        {field.state.meta.errors.map((error, idx) => (
                          <Error key={idx}>*{error?.message}</Error>
                        ))}
                      </>
                    )}
                  </div>
                  <TableField
                    columns={columns(field.state.value)}
                    id={pumpTopField}
                    isSuccess={isSuccess}
                    isLoading={isLoading}
                    loadingRows={9}
                    data={isSuccess ? data.result : []}
                    onRowSelect={(row, selected) => {
                      console.log({ data: row.original, selected });
                      wizard.send({
                        type: "setPumpTop",
                        pumpTop: selected
                          ? row.original
                          : pumpTopSchema.parse({}),
                      });
                    }}
                  />
                </div>
              )}
            />
          );
        }}
      />
      <div className="grid w-full grid-cols-2 items-center gap-4 justify-self-end-safe">
        <Button
          variant="default"
          type="submit"
          className="col-start-2 mr-auto ml-0 w-full max-w-md"
          onClick={() => {
            form.handleSubmit();
          }}
        >
          Build
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
        <div className="col-span-full">
          <form.AppForm>
            <form.SubmitErrorForm />
          </form.AppForm>
        </div>
      </div>
      <form.Subscribe
        selector={(state) => state.values.pump_top_id}
        children={() => {
          return <Total />;
        }}
      />
    </div>
  );
};

export { PumpTopForm };
