import * as React from "react";
import {
  Outlet,
  createRootRouteWithContext,
  useSearch,
} from "@tanstack/react-router";
import { TanStackRouterDevtools } from "@tanstack/react-router-devtools";
import { User } from "@/types/user";
import { createBuildAndPriceRouter } from "@routes/build-and-price/-router/router";
import { ProgressStoreProvider } from "./-store/progress/store";
import { pumpTopSchema } from "./-schemas/build";
type RootContext = {
  user: User;
};

declare module "@tanstack/react-router" {
  interface Register {
    router: ReturnType<typeof createBuildAndPriceRouter> &
      ReturnType<typeof createBuildAndPriceRouter>;
  }
}
export const Route = createRootRouteWithContext<RootContext>()({
  component: RootComponent,
});

function RootComponent() {
  const { step } = useSearch({ from: "/" });
  React.useEffect(() => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  }, [step]);
  return (
    <React.Fragment>
      <Outlet />
      <TanStackRouterDevtools />
    </React.Fragment>
  );
}
